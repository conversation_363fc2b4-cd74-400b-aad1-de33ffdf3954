import 'package:flutter/material.dart';
import 'database/database_helper.dart';
import 'persistence.dart' as globals;

class StopwatchRecordsLocal extends StatelessWidget {
  final String eventId;
  final DatabaseHelper dbHelper = DatabaseHelper.instance;

  StopwatchRecordsLocal({super.key, required this.eventId});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: dbHelper.getRecordsByEvent(eventId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Text('Error: ${snapshot.error}');
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(child: Text('No records found'));
        }

        // Process records to find start, lap, and stop times
        DateTime? startTime;
        DateTime? lapTime;
        DateTime? stopTime;

        for (var record in snapshot.data!) {
          final type = record['type'];
          final timestamp =
              DateTime.fromMillisecondsSinceEpoch(record['client_timestamp']);

          if (type == 'start') {
            startTime = timestamp;
          } else if (type == 'lap') {
            lapTime = timestamp;
          } else if (type == 'stop') {
            stopTime = timestamp;
          }
        }

        return ListView.builder(
          itemCount: snapshot.data!.length,
          itemBuilder: (context, index) {
            final record = snapshot.data![index];
            final type = record['type'];
            final timestamp =
                DateTime.fromMillisecondsSinceEpoch(record['client_timestamp']);
            final clientId = record['client_id'];
            final synced = record['synced'] == 1;

            String subtitle = '';
            if (startTime != null && stopTime != null && type == 'stop') {
              final duration = stopTime!.difference(startTime!);
              subtitle =
                  "Duration: ${(duration.inMilliseconds / 1000).toStringAsFixed(3)} seconds";
            }

            return ListTile(
              title: Text('$type - $clientId'),
              subtitle: Text(subtitle.isEmpty
                  ? '${timestamp.toString()} ${synced ? "(Synced)" : "(Not synced)"}'
                  : subtitle),
              trailing: Icon(
                synced ? Icons.cloud_done : Icons.cloud_upload,
                color: synced ? Colors.green : Colors.orange,
              ),
            );
          },
        );
      },
    );
  }
}
