import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../database/database_helper.dart';
import '../models/runner.dart';
import 'package:http/io_client.dart';

class ApiService {
  final String baseUrl;
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final bool trustSelfSigned;

  ApiService({required this.baseUrl, this.trustSelfSigned = true});

  http.Client get _client {
    if (!trustSelfSigned) return http.Client();
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    return IOClient(ioClient);
  }

  Future<Map<String, dynamic>?> syncRecords() async {
    try {
      final unsyncedRecords = await _dbHelper.getUnsyncedRecords();
      if (unsyncedRecords.isEmpty) return {'success': true, 'count': 0};

      final response = await http.post(
        Uri.parse('$baseUrl/api/stopwatch/records'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'records': unsyncedRecords}),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final List<dynamic> syncedRecords = responseData['records'];

        // Update local records with server IDs
        for (var record in syncedRecords) {
          await _dbHelper.markAsSynced(record['local_id'], record['server_id']);
        }

        return {'success': true, 'count': syncedRecords.length};
      } else {
        debugPrint('Failed to sync records: ${response}');
        return {
          'success': false,
          'error': 'Server returned ${response.statusCode}'
        };
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<List<Map<String, dynamic>>?> fetchEventRecords(String eventId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/stopwatch/events/$eventId/records'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['records']);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<bool> saveEventRunners(String eventId, List<Runner> runners) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/events/$eventId/runners'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'runners': runners.map((r) => r.toJson()).toList()}),
      );
      return response.statusCode == 200;
    } catch (_) {
      return false;
    }
  }

  Future<List<Runner>?> fetchEventRunners(String eventId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/events/$eventId/runners'),
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return (data['runners'] as List)
            .map((json) => Runner.fromJson(json))
            .toList();
      }
      return null;
    } catch (_) {
      return null;
    }
  }
}
