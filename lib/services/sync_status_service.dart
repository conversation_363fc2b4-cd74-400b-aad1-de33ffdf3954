import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import '../models/sync_status.dart';

class SyncStatusService {
  static final SyncStatusService _instance = SyncStatusService._internal();
  factory SyncStatusService() => _instance;
  SyncStatusService._internal();

  // Stream controllers for status updates
  final _syncStatusController = StreamController<SyncStatusInfo>.broadcast();
  final _networkStatusController = StreamController<NetworkStatusInfo>.broadcast();

  // Current status
  NetworkStatusInfo? _currentNetworkStatus;
  SyncStatusInfo? _lastSyncStatus;

  // Streams
  Stream<SyncStatusInfo> get syncStatusStream => _syncStatusController.stream;
  Stream<NetworkStatusInfo> get networkStatusStream => _networkStatusController.stream;

  // Getters
  NetworkStatusInfo? get currentNetworkStatus => _currentNetworkStatus;
  SyncStatusInfo? get lastSyncStatus => _lastSyncStatus;

  // Configuration
  String? _apiBaseUrl;
  Timer? _networkCheckTimer;
  StreamSubscription? _connectivitySubscription;

  void initialize(String apiBaseUrl) {
    _apiBaseUrl = apiBaseUrl;
    _startNetworkMonitoring();
  }

  void _startNetworkMonitoring() {
    // Initial network check
    _checkNetworkStatus();

    // Periodic network status checks (every 30 seconds)
    _networkCheckTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkNetworkStatus(),
    );

    // Listen to connectivity changes
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      _checkNetworkStatus();
    });
  }

  Future<void> _checkNetworkStatus() async {
    try {
      // Check basic connectivity
      final connectivityResults = await Connectivity().checkConnectivity();
      final hasConnection = connectivityResults.isNotEmpty && 
          connectivityResults.first != ConnectivityResult.none;

      NetworkStatus networkStatus;
      ServerStatus serverStatus = ServerStatus.unknown;
      int? latency;

      if (!hasConnection) {
        networkStatus = NetworkStatus.offline;
      } else {
        networkStatus = NetworkStatus.online;
        
        // Check server availability and latency
        if (_apiBaseUrl != null) {
          final serverCheck = await _checkServerStatus();
          serverStatus = serverCheck['status'] as ServerStatus;
          latency = serverCheck['latency'] as int?;
          
          // Adjust network status based on latency
          if (latency != null && latency > 3000) {
            networkStatus = NetworkStatus.poor;
          }
        }
      }

      final networkStatusInfo = NetworkStatusInfo(
        networkStatus: networkStatus,
        serverStatus: serverStatus,
        lastChecked: DateTime.now(),
        latency: latency,
      );

      _currentNetworkStatus = networkStatusInfo;
      _networkStatusController.add(networkStatusInfo);
    } catch (e) {
      // Handle errors gracefully
      final networkStatusInfo = NetworkStatusInfo(
        networkStatus: NetworkStatus.unknown,
        serverStatus: ServerStatus.unknown,
        lastChecked: DateTime.now(),
      );
      
      _currentNetworkStatus = networkStatusInfo;
      _networkStatusController.add(networkStatusInfo);
    }
  }

  Future<Map<String, dynamic>> _checkServerStatus() async {
    if (_apiBaseUrl == null) {
      return {'status': ServerStatus.unknown, 'latency': null};
    }

    try {
      final stopwatch = Stopwatch()..start();
      
      final response = await http.get(
        Uri.parse('$_apiBaseUrl/api/time'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));
      
      stopwatch.stop();
      final latency = stopwatch.elapsedMilliseconds;

      if (response.statusCode == 200) {
        ServerStatus status;
        if (latency > 5000) {
          status = ServerStatus.slow;
        } else {
          status = ServerStatus.available;
        }
        return {'status': status, 'latency': latency};
      } else {
        return {'status': ServerStatus.unavailable, 'latency': latency};
      }
    } catch (e) {
      return {'status': ServerStatus.unavailable, 'latency': null};
    }
  }

  void reportSyncStart(SyncType type) {
    final status = SyncStatusInfo(
      type: type,
      result: SyncResult.inProgress,
      timestamp: DateTime.now(),
    );
    
    _lastSyncStatus = status;
    _syncStatusController.add(status);
  }

  void reportSyncSuccess(SyncType type, {int? itemCount, String? message}) {
    final status = SyncStatusInfo(
      type: type,
      result: SyncResult.success,
      itemCount: itemCount,
      message: message,
      timestamp: DateTime.now(),
    );
    
    _lastSyncStatus = status;
    _syncStatusController.add(status);
  }

  void reportSyncFailure(SyncType type, {String? error, String? message}) {
    final status = SyncStatusInfo(
      type: type,
      result: SyncResult.failure,
      error: error,
      message: message,
      timestamp: DateTime.now(),
    );
    
    _lastSyncStatus = status;
    _syncStatusController.add(status);
  }

  void dispose() {
    _networkCheckTimer?.cancel();
    _connectivitySubscription?.cancel();
    _syncStatusController.close();
    _networkStatusController.close();
  }
}
