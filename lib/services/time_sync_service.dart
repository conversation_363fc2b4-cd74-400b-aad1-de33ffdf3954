import 'package:ntp/ntp.dart';
import 'dart:async';
import '../database/database_helper.dart';
import '../models/sync_status.dart';
import 'sync_status_service.dart';

class TimeSyncService {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  final SyncStatusService _statusService = SyncStatusService();
  final List<String> _ntpServers = [
    'time.google.com',
    'pool.ntp.org',
    'time.apple.com',
    'time.windows.com'
  ];

  Future<DateTime> getCurrentTime() async {
    _statusService.reportSyncStart(SyncType.timeSync);

    try {
      // Try to get NTP time from multiple servers
      List<DateTime> times = [];

      for (String server in _ntpServers) {
        try {
          final ntpTime = await NTP.now(lookUpAddress: server);
          times.add(ntpTime);
        } catch (e) {
          // Skip this server and try the next one
        }
      }

      if (times.isNotEmpty) {
        // Calculate average time
        final totalMillis = times.fold<int>(
            0, (sum, time) => sum + time.millisecondsSinceEpoch);
        final avgTime =
            DateTime.fromMillisecondsSinceEpoch(totalMillis ~/ times.length);

        // Calculate and store offset
        final localTime = DateTime.now();
        final offset = avgTime.difference(localTime).inMilliseconds;
        await _dbHelper.saveSetting('time_offset', offset.toString());

        _statusService.reportSyncSuccess(SyncType.timeSync,
            message: 'Synced with ${times.length} NTP servers');
        return avgTime;
      } else {
        _statusService.reportSyncFailure(SyncType.timeSync,
            error: 'No NTP servers available');
      }
    } catch (e) {
      _statusService.reportSyncFailure(SyncType.timeSync, error: e.toString());
    }

    // Use local time with stored offset
    final offsetStr = await _dbHelper.getSetting('time_offset');
    final offset = offsetStr != null ? int.parse(offsetStr) : 0;
    return DateTime.now().add(Duration(milliseconds: offset));
  }

  // Periodically sync time to maintain accuracy
  Timer? _syncTimer;

  void startPeriodicSync({Duration interval = const Duration(minutes: 15)}) {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(interval, (_) => getCurrentTime());
  }

  void stopPeriodicSync() {
    _syncTimer?.cancel();
  }
}
