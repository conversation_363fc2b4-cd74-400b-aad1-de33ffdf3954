import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'api_service.dart';
import '../models/app_config.dart';
import '../models/sync_status.dart';
import 'sync_status_service.dart';

class SyncManager {
  final ApiService _apiService;
  final AppConfig? _appConfig;
  final SyncStatusService _statusService = SyncStatusService();
  Timer? _syncTimer;
  StreamSubscription? _connectivitySubscription;

  SyncManager({
    required ApiService apiService,
    AppConfig? appConfig,
  })  : _apiService = apiService,
        _appConfig = appConfig;

  void startSync({Duration interval = const Duration(minutes: 2)}) {
    // Initial sync
    _syncAll();

    // Set up periodic sync
    _syncTimer = Timer.periodic(interval, (_) => _syncAll());

    // Set up connectivity listener to sync when connection is restored
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> results) {
      if (results.isNotEmpty && results.first != ConnectivityResult.none) {
        _syncAll();
      }
    });
  }

  Future<void> _syncAll() async {
    _statusService.reportSyncStart(SyncType.all);

    try {
      await _syncRecords();
      await _syncRunners();
      _statusService.reportSyncSuccess(SyncType.all);
    } catch (e) {
      _statusService.reportSyncFailure(SyncType.all, error: e.toString());
    }
  }

  Future<void> _syncRecords() async {
    _statusService.reportSyncStart(SyncType.records);

    try {
      final result = await _apiService.syncRecords();
      if (result != null && result['success'] == true) {
        final count = result['count'] as int? ?? 0;
        _statusService.reportSyncSuccess(SyncType.records, itemCount: count);
      } else {
        final error = result?['error'] as String? ?? 'Unknown error';
        _statusService.reportSyncFailure(SyncType.records, error: error);
      }
    } catch (e) {
      _statusService.reportSyncFailure(SyncType.records, error: e.toString());
    }
  }

  Future<void> _syncRunners() async {
    if (_appConfig == null) return;

    _statusService.reportSyncStart(SyncType.runners);

    try {
      await _appConfig.syncRunnersWithApi(_apiService);
      await _appConfig.loadRunnersFromApi(_apiService);
      _statusService.reportSyncSuccess(SyncType.runners);
    } catch (e) {
      _statusService.reportSyncFailure(SyncType.runners, error: e.toString());
    }
  }

  // Force an immediate sync
  Future<Map<String, dynamic>?> syncNow() async {
    _statusService.reportSyncStart(SyncType.all);

    try {
      await _syncRecords();
      await _syncRunners();
      _statusService.reportSyncSuccess(SyncType.all);
      return {'success': true};
    } catch (e) {
      _statusService.reportSyncFailure(SyncType.all, error: e.toString());
      return {'success': false, 'error': e.toString()};
    }
  }

  void stopSync() {
    _syncTimer?.cancel();
    _connectivitySubscription?.cancel();
  }
}
