import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import '../models/sync_status.dart';
import '../services/sync_status_service.dart';

class SyncStatusOverlay extends StatefulWidget {
  final Widget child;

  const SyncStatusOverlay({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  _SyncStatusOverlayState createState() => _SyncStatusOverlayState();
}

class _SyncStatusOverlayState extends State<SyncStatusOverlay>
    with TickerProviderStateMixin {
  final SyncStatusService _syncStatusService = SyncStatusService();
  
  StreamSubscription? _syncStatusSubscription;
  StreamSubscription? _networkStatusSubscription;
  
  SyncStatusInfo? _currentSyncStatus;
  NetworkStatusInfo? _currentNetworkStatus;
  
  late AnimationController _syncNotificationController;
  late AnimationController _pulseController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;
  
  Timer? _hideTimer;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _syncNotificationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: -100.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _syncNotificationController,
      curve: Curves.easeOutBack,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _syncNotificationController,
      curve: Curves.easeOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    // Start pulse animation for network indicator
    _pulseController.repeat(reverse: true);
    
    // Listen to status updates
    _syncStatusSubscription = _syncStatusService.syncStatusStream.listen(_onSyncStatusUpdate);
    _networkStatusSubscription = _syncStatusService.networkStatusStream.listen(_onNetworkStatusUpdate);
    
    // Get initial status
    _currentNetworkStatus = _syncStatusService.currentNetworkStatus;
    _currentSyncStatus = _syncStatusService.lastSyncStatus;
  }

  @override
  void dispose() {
    _syncStatusSubscription?.cancel();
    _networkStatusSubscription?.cancel();
    _syncNotificationController.dispose();
    _pulseController.dispose();
    _hideTimer?.cancel();
    super.dispose();
  }

  void _onSyncStatusUpdate(SyncStatusInfo status) {
    setState(() {
      _currentSyncStatus = status;
    });
    
    // Show sync notification
    _showSyncNotification();
  }

  void _onNetworkStatusUpdate(NetworkStatusInfo status) {
    setState(() {
      _currentNetworkStatus = status;
    });
  }

  void _showSyncNotification() {
    _hideTimer?.cancel();
    
    _syncNotificationController.forward();
    
    // Auto-hide after 3 seconds for success, 5 seconds for errors
    final hideDelay = _currentSyncStatus?.result == SyncResult.failure 
        ? const Duration(seconds: 5)
        : const Duration(seconds: 3);
    
    _hideTimer = Timer(hideDelay, () {
      if (mounted) {
        _syncNotificationController.reverse();
      }
    });
  }

  Color _getStatusColor(NetworkStatusInfo? networkStatus) {
    if (networkStatus == null) return CupertinoColors.systemGrey;
    
    if (!networkStatus.isOnline) return CupertinoColors.systemRed;
    if (!networkStatus.isServerAvailable) return CupertinoColors.systemOrange;
    if (networkStatus.serverStatus == ServerStatus.slow) return CupertinoColors.systemYellow;
    return CupertinoColors.systemGreen;
  }

  IconData _getStatusIcon(NetworkStatusInfo? networkStatus) {
    if (networkStatus == null) return CupertinoIcons.wifi_slash;
    
    if (!networkStatus.isOnline) return CupertinoIcons.wifi_slash;
    if (!networkStatus.isServerAvailable) return CupertinoIcons.exclamationmark_triangle;
    return CupertinoIcons.wifi;
  }

  Color _getSyncStatusColor(SyncResult result) {
    switch (result) {
      case SyncResult.success:
        return CupertinoColors.systemGreen;
      case SyncResult.failure:
        return CupertinoColors.systemRed;
      case SyncResult.inProgress:
        return CupertinoColors.systemBlue;
      case SyncResult.partial:
        return CupertinoColors.systemOrange;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        
        // Network status indicator (persistent, top-right)
        Positioned(
          top: MediaQuery.of(context).padding.top + 10,
          right: 16,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _currentNetworkStatus?.canSync == true ? 1.0 : _pulseAnimation.value,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(_currentNetworkStatus).withOpacity(0.9),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getStatusIcon(_currentNetworkStatus),
                        size: 12,
                        color: CupertinoColors.white,
                      ),
                      if (_currentNetworkStatus?.latency != null) ...[
                        const SizedBox(width: 4),
                        Text(
                          '${_currentNetworkStatus!.latency}ms',
                          style: const TextStyle(
                            color: CupertinoColors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        
        // Sync notification (temporary, top-center)
        if (_currentSyncStatus != null)
          Positioned(
            top: MediaQuery.of(context).padding.top + 50,
            left: 16,
            right: 16,
            child: AnimatedBuilder(
              animation: _syncNotificationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _slideAnimation.value),
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemBackground.resolveFrom(context),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: _getSyncStatusColor(_currentSyncStatus!.result),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: CupertinoColors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          if (_currentSyncStatus!.result == SyncResult.inProgress)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CupertinoActivityIndicator(radius: 8),
                            )
                          else
                            Icon(
                              _currentSyncStatus!.result == SyncResult.success
                                  ? CupertinoIcons.checkmark_circle_fill
                                  : CupertinoIcons.xmark_circle_fill,
                              color: _getSyncStatusColor(_currentSyncStatus!.result),
                              size: 16,
                            ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _currentSyncStatus!.displayMessage,
                              style: TextStyle(
                                color: CupertinoColors.label.resolveFrom(context),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}
