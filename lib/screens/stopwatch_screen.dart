import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import '../models/app_config.dart';
import '../services/time_sync_service.dart';
import '../database/database_helper.dart';
import '../models/stopwatch_record.dart';
import '../persistence.dart' as globals;
import '../models/runner.dart'; // Make sure this path matches where Runner is defined

class StopwatchScreen extends StatefulWidget {
  final Runner runner;
  final String eventId;
  final VoidCallback onRunnerUpdated;

  const StopwatchScreen({
    Key? key,
    required this.runner,
    required this.eventId,
    required this.onRunnerUpdated,
  }) : super(key: key);

  @override
  _StopwatchScreenState createState() => _StopwatchScreenState();
}

class _StopwatchScreenState extends State<StopwatchScreen> {
  final TimeSyncService _timeSyncService = TimeSyncService();
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  String _clientId = 'client_n/a';
  late Timer _timer;
  List<DateTime> _lapTimes = [];

  @override
  void initState() {
    super.initState();
    _loadClientId();
    _timer = Timer.periodic(const Duration(milliseconds: 16), (_) {
      setState(() {});
    });

    // Load existing lap times if available
    if (widget.runner.startTime != null) {
      _loadLapTimes();
    }
  }

  Future<void> _loadClientId() async {
    final id = await globals.getClientUniqueId();
    setState(() {
      _clientId = id;
    });
  }

  Future<void> _loadLapTimes() async {
    final records = await _dbHelper.getRecordsByEventAndClient(
      widget.eventId,
      _clientId,
      widget.runner.id,
    );

    final lapRecords = records.where((r) => r['type'] == 'lap').toList();

    setState(() {
      _lapTimes = [
        widget.runner.startTime!,
        ...lapRecords.map((r) => DateTime.parse(r['client_timestamp'])),
        if (widget.runner.finishTime != null) widget.runner.finishTime!,
      ];
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  Future<bool> _confirmReset() async {
    return await showCupertinoDialog<bool>(
          context: context,
          builder: (context) {
            return CupertinoAlertDialog(
              title: const Text('Reset Stopwatch'),
              content: const Text(
                  'Are you sure you want to reset the stopwatch? This action cannot be undone.'),
              actions: [
                CupertinoDialogAction(
                  child: const Text('Cancel'),
                  onPressed: () => Navigator.pop(context, false),
                ),
                CupertinoDialogAction(
                  isDestructiveAction: true,
                  child: const Text('Reset'),
                  onPressed: () => Navigator.pop(context, true),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  Future<void> _startStopwatch() async {
    final time = await _timeSyncService.getCurrentTime();
    setState(() {
      widget.runner.startTime = time;
      _lapTimes = [time];
    });

    _saveRecord('start', time);
    widget.onRunnerUpdated();
  }

  Future<void> _lapStopwatch() async {
    if (widget.runner.startTime == null) return;

    final time = await _timeSyncService.getCurrentTime();
    setState(() {
      _lapTimes.add(time);
    });

    _saveRecord('lap', time);
  }

  Future<void> _stopStopwatch() async {
    if (widget.runner.startTime == null) return;

    final time = await _timeSyncService.getCurrentTime();
    setState(() {
      widget.runner.finishTime = time;
      _lapTimes.add(time);
    });

    _saveRecord('stop', time);
    widget.onRunnerUpdated();
  }

  Future<void> _resetStopwatch() async {
    final confirmed = await _confirmReset();
    if (!confirmed) return;

    await _dbHelper.deleteRecordsByEventAndClient(
      widget.eventId,
      _clientId,
      widget.runner.id,
    );

    setState(() {
      widget.runner.startTime = null;
      widget.runner.finishTime = null;
      _lapTimes = [];
    });

    widget.onRunnerUpdated();
  }

  void _saveRecord(String type, DateTime time) async {
    final record = StopwatchRecord(
      eventId: widget.eventId,
      clientId: _clientId,
      type: type,
      clientTimestamp: time,
      additionalData: widget.runner.id,
    );

    await _dbHelper.saveRecord(record.toMap());
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    String twoDigitMilliseconds =
        twoDigits((duration.inMilliseconds.remainder(1000) ~/ 10));
    return '${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds.$twoDigitMilliseconds';
  }

  @override
  Widget build(BuildContext context) {
    final isRunning = widget.runner.isRunning;
    final isFinished = widget.runner.isFinished;
    final notStarted = widget.runner.startTime == null;

    Duration? elapsed;
    if (widget.runner.startTime != null) {
      elapsed = (widget.runner.finishTime ?? DateTime.now())
          .difference(widget.runner.startTime!);
    }

    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(widget.runner.name),
        trailing: isFinished || isRunning
            ? CupertinoButton(
                padding: EdgeInsets.zero,
                child: const Text('Reset'),
                onPressed: _resetStopwatch,
              )
            : null,
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Main timer display
            Expanded(
              flex: 2,
              child: Center(
                child: Text(
                  elapsed != null ? _formatDuration(elapsed) : '00:00:00.00',
                  style: const TextStyle(
                    fontSize: 60,
                    fontWeight: FontWeight.w200,
                    fontFeatures: [FontFeature.tabularFigures()],
                  ),
                ),
              ),
            ),

            // Control buttons
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 20.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Left button (Reset/Lap)
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      borderRadius: BorderRadius.circular(40),
                      color: isRunning
                          ? CupertinoColors.systemGrey.withOpacity(0.2)
                          : (isFinished
                              ? CupertinoColors.systemGrey.withOpacity(0.2)
                              : CupertinoColors.systemGrey.withOpacity(0.1)),
                      disabledColor:
                          CupertinoColors.systemGrey.withOpacity(0.1),
                      onPressed: notStarted
                          ? null
                          : (isRunning ? _lapStopwatch : null),
                      child: Text(
                        isRunning ? 'Lap' : (isFinished ? 'Reset' : ''),
                        style: TextStyle(
                          color: isRunning
                              ? CupertinoColors.white
                              : CupertinoColors.systemGrey,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),

                  // Right button (Start/Stop)
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      borderRadius: BorderRadius.circular(40),
                      color: isRunning
                          ? CupertinoColors.destructiveRed.withOpacity(0.2)
                          : CupertinoColors.activeGreen.withOpacity(0.2),
                      onPressed: isRunning
                          ? _stopStopwatch
                          : (isFinished ? null : _startStopwatch),
                      child: Text(
                        isRunning
                            ? 'Stop'
                            : (isFinished ? 'Finished' : 'Start'),
                        style: TextStyle(
                          color: isRunning
                              ? CupertinoColors.destructiveRed
                              : (isFinished
                                  ? CupertinoColors.systemGrey
                                  : CupertinoColors.activeGreen),
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Lap times list
            if (_lapTimes.isNotEmpty) ...[
              const Divider(height: 1),
              Expanded(
                flex: 3,
                child: ListView.builder(
                  itemCount: _lapTimes.length - 1,
                  itemBuilder: (context, index) {
                    // Display laps in reverse order (newest first)
                    final reversedIndex = _lapTimes.length - 2 - index;
                    if (reversedIndex < 0) return const SizedBox.shrink();

                    final lapStart = _lapTimes[reversedIndex];
                    final lapEnd = _lapTimes[reversedIndex + 1];
                    final lapDuration = lapEnd.difference(lapStart);

                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Lap ${reversedIndex + 1}',
                            style: const TextStyle(
                              fontSize: 16,
                              color: CupertinoColors.systemGrey,
                            ),
                          ),
                          Text(
                            _formatDuration(lapDuration),
                            style: const TextStyle(
                              fontSize: 16,
                              fontFeatures: [FontFeature.tabularFigures()],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
