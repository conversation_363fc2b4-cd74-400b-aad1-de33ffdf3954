import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../models/app_config.dart';
import '../services/api_service.dart';
import '../models/runner.dart';
import 'sync_test_screen.dart';

class SettingsScreen extends StatefulWidget {
  final AppConfig appConfig;
  final VoidCallback onConfigChanged;

  const SettingsScreen({
    Key? key,
    required this.appConfig,
    required this.onConfigChanged,
  }) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late TextEditingController _apiController;
  late TextEditingController _eventController;

  @override
  void initState() {
    super.initState();
    _apiController = TextEditingController(text: widget.appConfig.apiEndpoint);
    _eventController = TextEditingController(text: widget.appConfig.eventId);
  }

  @override
  void dispose() {
    _apiController.dispose();
    _eventController.dispose();
    super.dispose();
  }

  void _saveSettings() async {
    widget.appConfig.apiEndpoint = _apiController.text;
    widget.appConfig.eventId = _eventController.text;
    await widget.appConfig.save();
    // Fetch runners from API if event_id changed
    final apiService = ApiService(baseUrl: widget.appConfig.apiEndpoint);
    await widget.appConfig.loadRunnersFromApi(apiService);
    widget.onConfigChanged();
  }

  void _addRunner() {
    showCupertinoDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return CupertinoAlertDialog(
          title: const Text('Add Runner'),
          content: Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: CupertinoTextField(
              controller: controller,
              placeholder: 'Runner Name',
              autofocus: true,
            ),
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('Cancel'),
              onPressed: () => Navigator.pop(context),
            ),
            CupertinoDialogAction(
              child: const Text('Add'),
              onPressed: () {
                if (controller.text.isNotEmpty) {
                  setState(() {
                    final newId =
                        (widget.appConfig.runners.length + 1).toString();
                    widget.appConfig.runners
                        .add(Runner(id: newId, name: controller.text));
                    widget.appConfig.save();
                  });
                  Navigator.pop(context);
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('Settings'),
      ),
      child: SafeArea(
        child: ListView(
          children: [
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'API Configuration',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: CupertinoTextField(
                controller: _apiController,
                placeholder: 'API Endpoint',
                prefix: const Padding(
                  padding: EdgeInsets.only(left: 8.0),
                  child: Icon(CupertinoIcons.globe),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: CupertinoTextField(
                controller: _eventController,
                placeholder: 'Event ID',
                prefix: const Padding(
                  padding: EdgeInsets.only(left: 8.0),
                  child: Icon(CupertinoIcons.tag),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: CupertinoButton.filled(
                child: const Text('Save Settings'),
                onPressed: _saveSettings,
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Runners',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ...widget.appConfig.runners
                .map((runner) => Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 4.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              runner.name,
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                          CupertinoButton(
                            padding: EdgeInsets.zero,
                            child: const Icon(CupertinoIcons.delete),
                            onPressed: () {
                              setState(() {
                                widget.appConfig.runners.remove(runner);
                                widget.appConfig.save();
                              });
                            },
                          ),
                        ],
                      ),
                    ))
                .toList(),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: CupertinoButton(
                child: const Text('Add Runner'),
                onPressed: _addRunner,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: CupertinoButton.filled(
                child: const Text('Sync Status Test'),
                onPressed: () {
                  Navigator.push(
                    context,
                    CupertinoPageRoute(
                      builder: (context) =>
                          SyncTestScreen(appConfig: widget.appConfig),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
