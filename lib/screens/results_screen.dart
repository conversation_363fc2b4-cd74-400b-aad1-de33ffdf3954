import 'package:flutter/cupertino.dart';
import 'dart:async';
import '../models/app_config.dart';
import '../models/runner.dart';
import 'stopwatch_screen.dart';

class ResultsScreen extends StatefulWidget {
  final AppConfig appConfig;

  const ResultsScreen({Key? key, required this.appConfig}) : super(key: key);

  @override
  _ResultsScreenState createState() => _ResultsScreenState();
}

class _ResultsScreenState extends State<ResultsScreen> {
  late Timer _refreshTimer;

  @override
  void initState() {
    super.initState();
    _refreshTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _refreshTimer.cancel();
    super.dispose();
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return '--:--:--';

    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    String twoDigitMilliseconds =
        twoDigits((duration.inMilliseconds.remainder(1000) ~/ 10));
    return '${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds.$twoDigitMilliseconds';
  }

  @override
  Widget build(BuildContext context) {
    final activeRunners =
        widget.appConfig.runners.where((r) => r.isRunning).toList();
    final finishedRunners = widget.appConfig.runners
        .where((r) => r.isFinished)
        .toList()
      ..sort((a, b) => a.elapsedTime!.compareTo(b.elapsedTime!));
    final pendingRunners =
        widget.appConfig.runners.where((r) => r.startTime == null).toList();

    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('Race Results'),
      ),
      child: SafeArea(
        child: ListView(
          children: [
            if (activeRunners.isNotEmpty) ...[
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Active Runners',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ...activeRunners.map((runner) => _buildRunnerTile(
                    runner,
                    subtitle:
                        'Running - ${_formatDuration(runner.elapsedTime)}',
                    color: CupertinoColors.activeOrange,
                  )),
            ],
            if (finishedRunners.isNotEmpty) ...[
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Finished',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ...finishedRunners.map((runner) => _buildRunnerTile(
                    runner,
                    subtitle:
                        'Finished - ${_formatDuration(runner.elapsedTime)}',
                    color: CupertinoColors.activeGreen,
                  )),
            ],
            if (pendingRunners.isNotEmpty) ...[
              const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text(
                  'Pending',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ...pendingRunners.map((runner) => _buildRunnerTile(
                    runner,
                    subtitle: 'Not started',
                    color: CupertinoColors.inactiveGray,
                  )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRunnerTile(Runner runner,
      {required String subtitle, required Color color}) {
    return CupertinoListTile(
      title: Text(runner.name),
      subtitle: Text(subtitle),
      trailing: const CupertinoListTileChevron(),
      backgroundColor: color.withOpacity(0.1),
      onTap: () {
        Navigator.push(
          context,
          CupertinoPageRoute(
            builder: (context) => StopwatchScreen(
              runner: runner,
              eventId: widget.appConfig.eventId,
              onRunnerUpdated: () => setState(() {}),
            ),
          ),
        );
      },
    );
  }
}
