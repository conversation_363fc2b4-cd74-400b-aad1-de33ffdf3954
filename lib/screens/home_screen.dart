import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import '../models/app_config.dart';
import 'results_screen.dart';
import 'settings_screen.dart';

class HomeScreen extends StatefulWidget {
  final AppConfig appConfig;
  
  const HomeScreen({Key? key, required this.appConfig}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    return CupertinoTabScaffold(
      tabBar: CupertinoTabBar(
        items: const [
          BottomNavigationBarItem(
            icon: Icon(CupertinoIcons.list_bullet),
            label: 'Results',
          ),
          BottomNavigationBarItem(
            icon: Icon(CupertinoIcons.settings),
            label: 'Settings',
          ),
        ],
      ),
      tabBuilder: (context, index) {
        return CupertinoTabView(
          builder: (context) {
            switch (index) {
              case 0:
                return ResultsScreen(appConfig: widget.appConfig);
              case 1:
                return SettingsScreen(
                  appConfig: widget.appConfig,
                  onConfigChanged: () => setState(() {}),
                );
              default:
                return const SizedBox.shrink();
            }
          },
        );
      },
    );
  }
}