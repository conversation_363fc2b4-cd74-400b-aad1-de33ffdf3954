import 'package:flutter/cupertino.dart';
import 'dart:async';
import '../models/sync_status.dart';
import '../services/sync_status_service.dart';
import '../services/sync_manager.dart';
import '../services/time_sync_service.dart';
import '../services/api_service.dart';
import '../models/app_config.dart';

class SyncTestScreen extends StatefulWidget {
  final AppConfig appConfig;

  const SyncTestScreen({Key? key, required this.appConfig}) : super(key: key);

  @override
  _SyncTestScreenState createState() => _SyncTestScreenState();
}

class _SyncTestScreenState extends State<SyncTestScreen> {
  final SyncStatusService _statusService = SyncStatusService();
  late SyncManager _syncManager;
  late TimeSyncService _timeSyncService;
  
  StreamSubscription? _syncStatusSubscription;
  StreamSubscription? _networkStatusSubscription;
  
  SyncStatusInfo? _lastSyncStatus;
  NetworkStatusInfo? _networkStatus;
  List<SyncStatusInfo> _syncHistory = [];

  @override
  void initState() {
    super.initState();
    
    // Initialize services
    final apiService = ApiService(baseUrl: widget.appConfig.apiEndpoint);
    _syncManager = SyncManager(
      apiService: apiService,
      appConfig: widget.appConfig,
    );
    _timeSyncService = TimeSyncService();
    
    // Listen to status updates
    _syncStatusSubscription = _statusService.syncStatusStream.listen((status) {
      setState(() {
        _lastSyncStatus = status;
        _syncHistory.insert(0, status);
        // Keep only last 10 entries
        if (_syncHistory.length > 10) {
          _syncHistory = _syncHistory.take(10).toList();
        }
      });
    });
    
    _networkStatusSubscription = _statusService.networkStatusStream.listen((status) {
      setState(() {
        _networkStatus = status;
      });
    });
    
    // Get initial status
    _networkStatus = _statusService.currentNetworkStatus;
    _lastSyncStatus = _statusService.lastSyncStatus;
  }

  @override
  void dispose() {
    _syncStatusSubscription?.cancel();
    _networkStatusSubscription?.cancel();
    super.dispose();
  }

  void _triggerRecordSync() async {
    try {
      await _syncManager.syncNow();
    } catch (e) {
      // Error handling is done in the sync manager
    }
  }

  void _triggerTimeSync() async {
    try {
      await _timeSyncService.getCurrentTime();
    } catch (e) {
      // Error handling is done in the time sync service
    }
  }

  Color _getStatusColor(SyncResult result) {
    switch (result) {
      case SyncResult.success:
        return CupertinoColors.systemGreen;
      case SyncResult.failure:
        return CupertinoColors.systemRed;
      case SyncResult.inProgress:
        return CupertinoColors.systemBlue;
      case SyncResult.partial:
        return CupertinoColors.systemOrange;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
           '${timestamp.minute.toString().padLeft(2, '0')}:'
           '${timestamp.second.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('Sync Status Test'),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Network Status Section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: CupertinoColors.systemGrey6.resolveFrom(context),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Network Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_networkStatus != null) ...[
                      Text('Status: ${_networkStatus!.statusText}'),
                      Text('Network: ${_networkStatus!.networkStatus.name}'),
                      Text('Server: ${_networkStatus!.serverStatus.name}'),
                      if (_networkStatus!.latency != null)
                        Text('Latency: ${_networkStatus!.latency}ms'),
                      Text('Last checked: ${_formatTimestamp(_networkStatus!.lastChecked)}'),
                    ] else
                      const Text('No network status available'),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Manual Sync Buttons
              const Text(
                'Manual Sync',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: CupertinoButton.filled(
                      onPressed: _triggerRecordSync,
                      child: const Text('Sync Records'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: CupertinoButton.filled(
                      onPressed: _triggerTimeSync,
                      child: const Text('Sync Time'),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Last Sync Status
              if (_lastSyncStatus != null) ...[
                const Text(
                  'Last Sync',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _getStatusColor(_lastSyncStatus!.result).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getStatusColor(_lastSyncStatus!.result),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Type: ${_lastSyncStatus!.type.name}'),
                      Text('Result: ${_lastSyncStatus!.result.name}'),
                      Text('Message: ${_lastSyncStatus!.displayMessage}'),
                      if (_lastSyncStatus!.itemCount != null)
                        Text('Items: ${_lastSyncStatus!.itemCount}'),
                      if (_lastSyncStatus!.error != null)
                        Text('Error: ${_lastSyncStatus!.error}'),
                      Text('Time: ${_formatTimestamp(_lastSyncStatus!.timestamp)}'),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
              ],
              
              // Sync History
              const Text(
                'Sync History',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: _syncHistory.isEmpty
                    ? const Center(child: Text('No sync history'))
                    : ListView.builder(
                        itemCount: _syncHistory.length,
                        itemBuilder: (context, index) {
                          final sync = _syncHistory[index];
                          return Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: _getStatusColor(sync.result).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: _getStatusColor(sync.result).withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(sync.result),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        sync.displayMessage,
                                        style: const TextStyle(fontWeight: FontWeight.w500),
                                      ),
                                      Text(
                                        '${sync.type.name} • ${_formatTimestamp(sync.timestamp)}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: CupertinoColors.systemGrey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
