import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';
import 'package:logger/logger.dart';
import 'dart:io';

Logger logger = Logger();

Future<String> getClientUniqueId() async {
  final prefs = await SharedPreferences.getInstance();
  const key = 'clientUniqueId';
  String? clientUniqueId = prefs.getString(key);

  if (clientUniqueId == null) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(100000);
    clientUniqueId = '${Platform.operatingSystem}_$timestamp$random';
    prefs.setString(key, clientUniqueId);
  }

  return clientUniqueId;
}
