import 'package:flutter/material.dart';
import 'dart:async';

class StopwatchScreen2 extends StatefulWidget {
  @override
  _StopwatchScreenState createState() => _StopwatchScreenState();
}

class _StopwatchScreenState extends State<StopwatchScreen2> {
  Stopwatch _stopwatch = Stopwatch();
  late Timer _timer;
  List<String> _laps = [];

  void _startTimer() {
    _timer = Timer.periodic(Duration(milliseconds: 32), (timer) {
      if (mounted) setState(() {});
    });
  }

  void _startStopwatch() {
    _stopwatch.start();
    _startTimer();
  }

  void _stopStopwatch() {
    _stopwatch.stop();
    _timer?.cancel();
    setState(() {});
  }

  void _resetStopwatch() {
    _stopwatch.reset();
    _laps.clear();
    setState(() {});
  }

  void _recordLap() {
    setState(() {
      _laps.add(_formattedTime());
    });
  }

  String _formattedTime() {
    final duration = _stopwatch.elapsed;
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    final milliseconds = (duration.inMilliseconds.remainder(1000) / 10).toInt();
    if (hours == '00') {
      return "$minutes:$seconds,${milliseconds.toString().padLeft(2, '0')}";
    }
    return "$hours:$minutes:$seconds,${milliseconds.toString().padLeft(2, '0')}";
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        bottom: TabBar(
          tabs: const [
            Tab(text: 'Stopwatch'),
            Tab(text: 'Timer'),
          ],
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: LayoutBuilder(
              builder: (context, constraints) {
                double fontSize = constraints.maxWidth *
                    (_stopwatch.elapsed > const Duration(hours: 1)
                        ? 0.13
                        : 0.17);
                return Center(
                  child: Text(
                    _formattedTime(),
                    style: TextStyle(
                      fontSize: fontSize,
                      fontFeatures: const [FontFeature.tabularFigures()],
                      fontWeight: FontWeight.w200,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed:
                      _stopwatch.isRunning ? _recordLap : _resetStopwatch,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    shape: const CircleBorder(),
                    padding: const EdgeInsets.all(20),
                  ),
                  child: Text(
                    _stopwatch.isRunning ? 'Lap' : 'Reset',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w200,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed:
                      _stopwatch.isRunning ? _stopStopwatch : _startStopwatch,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _stopwatch.isRunning
                        ? const Color.fromARGB(255, 47, 16, 13)
                        : Color.fromARGB(255, 38, 43, 34),
                    shape: const CircleBorder(),
                    padding: const EdgeInsets.all(20),
                  ),
                  child: _stopwatch.isRunning
                      ? Text('Stop',
                          style: const TextStyle(
                              fontWeight: FontWeight.w200,
                              fontSize: 16,
                              color: Color.fromARGB(255, 234, 85, 68)))
                      : Text('Start',
                          style: const TextStyle(
                              fontWeight: FontWeight.w200,
                              fontSize: 16,
                              color: Color.fromARGB(255, 183, 216, 154))),
                ),
              ],
            ),
          ),
          const Divider(color: Colors.white54),
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: ListView.builder(
                itemExtent: 56.0,
                itemCount: _laps.length,
                itemBuilder: (context, index) {
                  //listtile with bottom line
                  return Container(
                      padding: const EdgeInsets.all(0),
                      margin: const EdgeInsets.all(0),
                      height: 72,
                      child: Column(
                        children: [
                          ListTile(
                            contentPadding: const EdgeInsets.all(0),
                            visualDensity:
                                VisualDensity(vertical: -4), // to compact
                            leading: Text(
                              'Lap ${_laps.length - index}',
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold),
                            ),
                            trailing: Text(
                              _laps[_laps.length - index - 1],
                              // green color when smallest lap time
                              // red color when largest lap time
                              // white otherwise
                              style: TextStyle(
                                  color: _laps[_laps.length - index - 1] ==
                                          _laps.reduce((a, b) =>
                                              a.compareTo(b) > 0 ? a : b)
                                      ? const Color.fromARGB(255, 234, 85, 68)
                                      : _laps[_laps.length - index - 1] ==
                                              _laps.reduce((a, b) =>
                                                  a.compareTo(b) < 0 ? a : b)
                                          ? const Color.fromARGB(
                                              255, 183, 216, 154)
                                          : Colors.white),
                            ),
                          ),
                          const Divider(color: Colors.white54),
                        ],
                      ));
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
