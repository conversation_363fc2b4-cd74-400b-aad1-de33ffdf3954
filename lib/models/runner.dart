import 'dart:convert';

class Runner {
  final String id;
  final String name;
  DateTime? startTime;
  DateTime? finishTime;
  final Map<String, dynamic>? additionalData;

  Runner({
    required this.id,
    required this.name,
    this.startTime,
    this.finishTime,
    this.additionalData,
  });

  bool get isFinished => startTime != null && finishTime != null;
  bool get isRunning => startTime != null && finishTime == null;
  Duration? get elapsedTime {
    if (startTime == null) return null;
    return (finishTime ?? DateTime.now()).difference(startTime!);
  }

  factory Runner.fromJson(dynamic json) {
    return Runner(
      id: json['runner_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      startTime: json['start_time'] != null
          ? DateTime.tryParse(json['start_time'])
          : (json['startTime'] != null
              ? DateTime.tryParse(json['startTime'])
              : null),
      finishTime: json['finish_time'] != null
          ? DateTime.tryParse(json['finish_time'])
          : (json['finishTime'] != null
              ? DateTime.tryParse(json['finishTime'])
              : null),
      additionalData: json['additional_data'] is String
          ? _tryDecode(json['additional_data'])
          : (json['additional_data'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'startTime': startTime?.toIso8601String(),
      'finishTime': finishTime?.toIso8601String(),
      'additional_data': additionalData,
    };
  }

  static Map<String, dynamic>? _tryDecode(String? data) {
    if (data == null) return null;
    try {
      return data.isNotEmpty
          ? Map<String, dynamic>.from(jsonDecode(data))
          : null;
    } catch (_) {
      return null;
    }
  }
}
