import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';
import 'package:accurace/services/api_service.dart';
import 'package:accurace/models/runner.dart';

class AppConfig {
  String apiEndpoint;
  String eventId;
  List<Runner> runners;

  AppConfig({
    required this.apiEndpoint,
    required this.eventId,
    required this.runners,
  });

  static Future<AppConfig> load() async {
    final prefs = await SharedPreferences.getInstance();

    // Load saved config or use defaults
    final apiEndpoint = prefs.getString('apiEndpoint') ??
        'https://grapph.com/accurace/webservice';
    final eventId = prefs.getString('eventId') ?? 'race1';

    // Load runners from preferences
    final runnersList = prefs.getStringList('runners') ?? [];
    final runners = runnersList
        .map((jsonStr) => Runner.fromJson(jsonDecode(jsonStr)))
        .toList();

    if (runners.isEmpty) {
      runners.add(Runner(id: '1', name: 'Runner 1'));
      runners.add(Runner(id: '2', name: 'Runner 2'));
      runners.add(Runner(id: '3', name: 'Runner 3'));
    }

    return AppConfig(
      apiEndpoint: apiEndpoint,
      eventId: eventId,
      runners: runners,
    );
  }

  Future<void> save() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('apiEndpoint', apiEndpoint);
    await prefs.setString('eventId', eventId);

    // Save runners to preferences
    final runnersList = runners
        .map((runner) => runner.toJson())
        .map((json) => jsonEncode(json))
        .toList();
    await prefs.setStringList('runners', runnersList);
  }

  Future<void> syncRunnersWithApi(ApiService apiService) async {
    // Save local runners to API
    await apiService.saveEventRunners(eventId, runners);
  }

  Future<void> loadRunnersFromApi(ApiService apiService) async {
    final apiRunners = await apiService.fetchEventRunners(eventId);
    if (apiRunners != null && apiRunners.isNotEmpty) {
      runners = apiRunners;
      await save();
    }
  }

// Removed local Runner class; now using Runner from 'package:accurace/models/runner.dart'
}
