enum SyncType {
  records,
  runners,
  timeSync,
  all,
}

enum SyncResult {
  success,
  failure,
  partial,
  inProgress,
}

enum NetworkStatus {
  online,
  offline,
  poor,
  unknown,
}

enum ServerStatus {
  available,
  unavailable,
  slow,
  unknown,
}

class SyncStatusInfo {
  final SyncType type;
  final SyncResult result;
  final String? message;
  final int? itemCount;
  final DateTime timestamp;
  final String? error;

  const SyncStatusInfo({
    required this.type,
    required this.result,
    this.message,
    this.itemCount,
    required this.timestamp,
    this.error,
  });

  String get displayMessage {
    switch (type) {
      case SyncType.records:
        if (result == SyncResult.success) {
          return itemCount != null && itemCount! > 0
              ? 'Synced $itemCount records'
              : 'Records up to date';
        } else if (result == SyncResult.inProgress) {
          return 'Syncing records...';
        } else {
          return 'Failed to sync records';
        }
      case SyncType.runners:
        if (result == SyncResult.success) {
          return 'Runners synchronized';
        } else if (result == SyncResult.inProgress) {
          return 'Syncing runners...';
        } else {
          return 'Failed to sync runners';
        }
      case SyncType.timeSync:
        if (result == SyncResult.success) {
          return 'Time synchronized';
        } else if (result == SyncResult.inProgress) {
          return 'Syncing time...';
        } else {
          return 'Time sync failed';
        }
      case SyncType.all:
        if (result == SyncResult.success) {
          return 'All data synchronized';
        } else if (result == SyncResult.inProgress) {
          return 'Synchronizing...';
        } else {
          return 'Sync failed';
        }
    }
  }
}

class NetworkStatusInfo {
  final NetworkStatus networkStatus;
  final ServerStatus serverStatus;
  final DateTime lastChecked;
  final int? latency; // in milliseconds

  const NetworkStatusInfo({
    required this.networkStatus,
    required this.serverStatus,
    required this.lastChecked,
    this.latency,
  });

  bool get isOnline => networkStatus == NetworkStatus.online;
  bool get isServerAvailable => serverStatus == ServerStatus.available;
  bool get canSync => isOnline && isServerAvailable;

  String get statusText {
    if (!isOnline) return 'Offline';
    if (!isServerAvailable) return 'Server unavailable';
    if (serverStatus == ServerStatus.slow) return 'Slow connection';
    return 'Online';
  }
}
