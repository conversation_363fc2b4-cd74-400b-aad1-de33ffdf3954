class StopwatchRecord {
  final int? id;
  final String eventId;
  final String clientId;
  final String type;
  final DateTime clientTimestamp;
  final DateTime? serverTimestamp;
  final String additionalData;
  final bool synced;
  final String? serverId;
  
  StopwatchRecord({
    this.id,
    required this.eventId,
    required this.clientId,
    required this.type,
    required this.clientTimestamp,
    this.serverTimestamp,
    this.additionalData = '',
    this.synced = false,
    this.serverId,
  });
  
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'event_id': eventId,
      'client_id': clientId,
      'type': type,
      'client_timestamp': clientTimestamp.millisecondsSinceEpoch,
      'server_timestamp': serverTimestamp?.millisecondsSinceEpoch,
      'additional_data': additionalData,
      'synced': synced ? 1 : 0,
      'server_id': serverId,
    };
  }
  
  factory StopwatchRecord.fromMap(Map<String, dynamic> map) {
    return StopwatchRecord(
      id: map['id'],
      eventId: map['event_id'],
      clientId: map['client_id'],
      type: map['type'],
      clientTimestamp: DateTime.fromMillisecondsSinceEpoch(map['client_timestamp']),
      serverTimestamp: map['server_timestamp'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['server_timestamp']) 
          : null,
      additionalData: map['additional_data'] ?? '',
      synced: map['synced'] == 1,
      serverId: map['server_id'],
    );
  }
}