import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('stopwatch.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(path, version: 1, onCreate: _createDB);
  }

  Future _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE stopwatch_records(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        event_id TEXT NOT NULL,
        client_id TEXT NOT NULL,
        type TEXT NOT NULL,
        client_timestamp INTEGER NOT NULL,
        server_timestamp INTEGER,
        additional_data TEXT,
        synced INTEGER DEFAULT 0,
        server_id TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE settings(
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      )
    ''');
  }

  // Settings methods
  Future<void> saveSetting(String key, String value) async {
    final db = await database;
    await db.insert(
      'settings',
      {'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getSetting(String key) async {
    final db = await database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: [key],
    );

    if (result.isNotEmpty) {
      return result.first['value'] as String;
    }
    return null;
  }

  // Stopwatch record methods
  Future<int> saveRecord(Map<String, dynamic> record) async {
    final db = await database;
    return await db.insert('stopwatch_records', record);
  }

  Future<List<Map<String, dynamic>>> getUnsyncedRecords() async {
    final db = await database;
    return await db.query(
      'stopwatch_records',
      where: 'synced = ?',
      whereArgs: [0],
    );
  }

  Future<void> markAsSynced(int id, String serverId) async {
    final db = await database;
    await db.update(
      'stopwatch_records',
      {'synced': 1, 'server_id': serverId},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<Map<String, dynamic>>> getRecordsByEvent(String eventId) async {
    final db = await database;
    return await db.query(
      'stopwatch_records',
      where: 'event_id = ?',
      whereArgs: [eventId],
      orderBy: 'client_timestamp DESC',
    );
  }

  Future<List<Map<String, dynamic>>> getRecordsByEventAndClient(
    String eventId,
    String clientId,
    String runnerId,
  ) async {
    final db = await database;
    return await db.query(
      'stopwatch_records',
      where: 'event_id = ? AND client_id = ? AND additional_data = ?',
      whereArgs: [eventId, clientId, runnerId],
      orderBy: 'client_timestamp ASC',
    );
  }

  Future<void> deleteRecordsByEvent(String eventId) async {
    final db = await database;
    await db.delete(
      'stopwatch_records',
      where: 'event_id = ?',
      whereArgs: [eventId],
    );
  }

  Future<void> deleteRecordsByEventAndClient(
    String eventId,
    String clientId,
    String runnerId,
  ) async {
    final db = await database;
    await db.delete(
      'stopwatch_records',
      where: 'event_id = ? AND client_id = ? AND additional_data = ?',
      whereArgs: [eventId, clientId, runnerId],
    );
  }
}
