// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyASEK_RDTcfu6WxUr9QazX9zVa2LfUFRnU',
    appId: '1:179920943937:web:c39c88ec7780b3f07f7fc0',
    messagingSenderId: '179920943937',
    projectId: 'accurace-1',
    authDomain: 'accurace-1.firebaseapp.com',
    storageBucket: 'accurace-1.appspot.com',
    measurementId: 'G-5PL046D857',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDN_2iRm-tKnGfE_eYVWX_2otBcXA60Xfg',
    appId: '1:179920943937:android:609f24b751e2bd527f7fc0',
    messagingSenderId: '179920943937',
    projectId: 'accurace-1',
    storageBucket: 'accurace-1.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC7LQ-Q67nHA40Qh4YyeF4YVHuCWNljLks',
    appId: '1:179920943937:ios:421391f798ed9f357f7fc0',
    messagingSenderId: '179920943937',
    projectId: 'accurace-1',
    storageBucket: 'accurace-1.appspot.com',
    iosBundleId: 'com.chastia.one.beta',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC7LQ-Q67nHA40Qh4YyeF4YVHuCWNljLks',
    appId: '1:179920943937:ios:8c014efb8479b1957f7fc0',
    messagingSenderId: '179920943937',
    projectId: 'accurace-1',
    storageBucket: 'accurace-1.appspot.com',
    iosBundleId: 'com.example.accurace',
  );
}
