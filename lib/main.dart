import 'package:accurace/screens/home_screen.dart';
import 'package:flutter/cupertino.dart';
import 'services/time_sync_service.dart';
import 'services/api_service.dart';
import 'services/sync_manager.dart';
import 'services/sync_status_service.dart';
import 'models/app_config.dart';
import 'widgets/sync_status_overlay.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load app configuration
  final appConfig = await AppConfig.load();

  // Initialize services
  final apiService = ApiService(baseUrl: appConfig.apiEndpoint);
  final syncStatusService = SyncStatusService();
  final syncManager = SyncManager(
    apiService: apiService,
    appConfig: appConfig,
  );

  // Initialize sync status service with API endpoint
  syncStatusService.initialize(appConfig.apiEndpoint);

  // Start sync services
  TimeSyncService().startPeriodicSync();
  syncManager.startSync();

  runApp(MyApp(appConfig: appConfig));
}

class MyApp extends StatelessWidget {
  final AppConfig appConfig;

  const MyApp({Key? key, required this.appConfig}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CupertinoApp(
      debugShowCheckedModeBanner: false,
      title: 'AccuRace',
      theme: const CupertinoThemeData(
        brightness: Brightness.dark,
        primaryColor: CupertinoColors.systemOrange,
      ),
      home: SyncStatusOverlay(
        child: HomeScreen(appConfig: appConfig),
      ),
    );
  }
}
