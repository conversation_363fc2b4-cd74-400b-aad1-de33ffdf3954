{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Projects/accurace/android/app/.cxx/Debug/5l4m4u5g/arm64-v8a", "source": "/usr/local/Caskroom/flutter/3.3.6/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}