{"buildFiles": ["/usr/local/Caskroom/flutter/3.3.6/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Projects/accurace/android/app/.cxx/Debug/5l4m4u5g/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Projects/accurace/android/app/.cxx/Debug/5l4m4u5g/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}