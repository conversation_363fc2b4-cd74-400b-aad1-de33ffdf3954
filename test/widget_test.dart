// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:accurace/main.dart';
import 'package:accurace/models/app_config.dart';
import 'package:accurace/models/runner.dart';

void main() {
  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Create a test app config
    final testConfig = AppConfig(
      apiEndpoint: 'https://test.example.com',
      eventId: 'test_event',
      runners: [
        Runner(id: '1', name: 'Test Runner 1'),
        Runner(id: '2', name: 'Test Runner 2'),
      ],
    );

    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp(appConfig: testConfig));

    // Verify that the app loads
    expect(find.text('Results'), findsOneWidget);
    expect(find.text('Settings'), findsOneWidget);
  });
}
