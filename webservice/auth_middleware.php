<?php
/**
 * API Authentication middleware
 */
function authenticateRequest() {
    // Check for API key in header
    $headers = getallheaders();
    $apiKey = isset($headers['X-API-Key']) ? $headers['X-API-Key'] : null;
    
    // If no API key in header, check query string
    if (!$apiKey && isset($_GET['api_key'])) {
        $apiKey = $_GET['api_key'];
    }
    
    // Validate API key
    if (!$apiKey || $apiKey !== API_KEY) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        exit;
    }
    
    return true;
}

/**
 * Optional: Validate API key against database
 */
function validateApiKeyFromDb($apiKey) {
    $db = getDbConnection();
    if (!$db) {
        return false;
    }
    
    try {
        $stmt = $db->prepare("
            SELECT id FROM api_keys 
            WHERE api_key = ?
        ");
        
        $stmt->execute([$apiKey]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // Update last used timestamp
            $updateStmt = $db->prepare("
                UPDATE api_keys 
                SET last_used_at = NOW() 
                WHERE id = ?
            ");
            $updateStmt->execute([$result['id']]);
            
            return true;
        }
        
        return false;
        
    } catch (PDOException $e) {
        error_log("Error validating API key: " . $e->getMessage());
        return false;
    }
}