<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '5432');
define('DB_NAME', 'stopwatch_db');
define('DB_USER', 'accurace');
define('DB_PASS', 'sdfh32nvdweBEId.e');

// API settings
define('API_KEY', 'AC8AC8715128676A');
define('ALLOW_CORS', true);

// Error reporting
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Timezone
date_default_timezone_set('UTC');

// Helper function to get database connection
function getDbConnection()
{
    $connection_string = sprintf(
        "pgsql:host=%s;port=%s;dbname=%s;user=%s;password=%s",
        DB_HOST,
        DB_PORT,
        DB_NAME,
        DB_USER,
        DB_PASS
    );

    try {
        $pdo = new PDO($connection_string);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        return null;
    }
}
