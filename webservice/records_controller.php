<?php

/**
 * <PERSON>les stopwatch records requests
 */
function handleRecordsRequest()
{
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'POST':
            // Handle record creation/sync
            saveRecords();
            break;

        case 'GET':
            // Get all records (could add pagination)
            getAllRecords();
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
}

/**
 * <PERSON><PERSON> requests for records of a specific event
 */
function handleEventRecordsRequest($eventId)
{
    $method = $_SERVER['REQUEST_METHOD'];

    if ($method === 'GET') {
        getEventRecords($eventId);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
}

/**
 * <PERSON>les requests for runners of a specific event
 */
function handleEventRunnersRequest($eventId)
{
    $method = $_SERVER['REQUEST_METHOD'];
    if ($method === 'GET') {
        getEventRunners($eventId);
    } elseif ($method === 'POST') {
        saveEventRunners($eventId);
    } else {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
    }
}


/**
 * Save batch of records from client
 */
function saveRecords()
{
    // Get JSON data from request body
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!isset($data['records']) || !is_array($data['records'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid request format']);
        return;
    }

    $db = getDbConnection();
    if (!$db) {
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
        return;
    }

    try {
        // Begin transaction
        $db->beginTransaction();

        $syncedRecords = [];

        foreach ($data['records'] as $record) {
            // Validate required fields
            if (
                !isset($record['event_id']) || !isset($record['client_id']) ||
                !isset($record['type']) || !isset($record['client_timestamp'])
            ) {
                continue; // Skip invalid records
            }

            // Insert record
            $stmt = $db->prepare("
                INSERT INTO stopwatch_records 
                (event_id, client_id, type, client_timestamp, additional_data, created_at) 
                VALUES (?, ?, ?, to_timestamp(?), ?, NOW())
                ON CONFLICT (event_id, client_id, type, client_timestamp) DO NOTHING
                RETURNING id
            ");

            $stmt->execute([
                $record['event_id'],
                $record['client_id'],
                $record['type'],
                $record['client_timestamp'] / 1000, // Convert milliseconds to seconds
                $record['additional_data'] ?? ''
            ]);

            $serverId = $stmt->fetchColumn();

            // Add to synced records response
            $syncedRecords[] = [
                'local_id' => $record['id'],
                'server_id' => $serverId
            ];
        }

        // Commit transaction
        $db->commit();

        // Return success response with synced records
        echo json_encode([
            'success' => true,
            'records' => $syncedRecords
        ]);
    } catch (PDOException $e) {
        // Rollback transaction on error
        $db->rollBack();
        error_log("Error saving records: " . $e->getMessage());

        http_response_code(500);
        echo json_encode(['error' => 'Failed to save records']);
    }
}

/**
 * Get all records
 */
function getAllRecords()
{
    $db = getDbConnection();
    if (!$db) {
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
        return;
    }

    try {
        $stmt = $db->query("
            SELECT 
                id, event_id, client_id, type, 
                extract(epoch from client_timestamp) * 1000 as client_timestamp,
                additional_data, created_at
            FROM stopwatch_records
            ORDER BY client_timestamp DESC
            LIMIT 1000
        ");

        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'records' => $records
        ]);
    } catch (PDOException $e) {
        error_log("Error fetching records: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch records']);
    }
}

/**
 * Get records for a specific event
 */
function getEventRecords($eventId)
{
    $db = getDbConnection();
    if (!$db) {
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
        return;
    }

    try {
        $stmt = $db->prepare("
            SELECT 
                id, event_id, client_id, type, 
                extract(epoch from client_timestamp) * 1000 as client_timestamp,
                additional_data, created_at
            FROM stopwatch_records
            WHERE event_id = ?
            ORDER BY client_timestamp DESC
        ");

        $stmt->execute([$eventId]);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'event_id' => $eventId,
            'records' => $records
        ]);
    } catch (PDOException $e) {
        error_log("Error fetching event records: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch event records']);
    }
}

/**
 * Get runners for a specific event
 */
function getEventRunners($eventId)
{
    $db = getDbConnection();
    if (!$db) {
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
        return;
    }

    try {
        $stmt = $db->prepare("
            SELECT id, event_id, runner_id, name, start_time, finish_time, additional_data
            FROM event_runners
            WHERE event_id = ?
            ORDER BY id ASC
        ");
        $stmt->execute([$eventId]);
        $runners = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'event_id' => $eventId,
            'runners' => $runners
        ]);
    } catch (PDOException $e) {
        error_log("Error fetching event runners: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch event runners']);
    }
}

/**
 * Save runners for a specific event (replace all runners for the event)
 */
function saveEventRunners($eventId)
{
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);

    if (!isset($data['runners']) || !is_array($data['runners'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid request format']);
        return;
    }

    $db = getDbConnection();
    if (!$db) {
        http_response_code(500);
        echo json_encode(['error' => 'Database connection failed']);
        return;
    }

    try {
        $db->beginTransaction();

        // Remove existing runners for this event
        $stmtDelete = $db->prepare("DELETE FROM event_runners WHERE event_id = ?");
        $stmtDelete->execute([$eventId]);

        // Insert new runners
        $stmtInsert = $db->prepare("
            INSERT INTO event_runners (event_id, runner_id, name, start_time, finish_time, additional_data)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        foreach ($data['runners'] as $runner) {
            $runnerId = $runner['id'] ?? null;
            $name = $runner['name'] ?? '';
            $startTime = isset($runner['startTime']) ? date('Y-m-d H:i:s', strtotime($runner['startTime'])) : null;
            $finishTime = isset($runner['finishTime']) ? date('Y-m-d H:i:s', strtotime($runner['finishTime'])) : null;
            $additionalData = isset($runner['additional_data']) ? json_encode($runner['additional_data']) : null;

            $stmtInsert->execute([
                $eventId,
                $runnerId,
                $name,
                $startTime,
                $finishTime,
                $additionalData
            ]);
        }

        $db->commit();

        echo json_encode(['success' => true]);
    } catch (PDOException $e) {
        $db->rollBack();
        error_log("Error saving event runners: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(['error' => 'Failed to save event runners']);
    }
}
