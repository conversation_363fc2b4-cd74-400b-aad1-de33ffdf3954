-- Create database (run this separately)
-- CREATE DATABASE stopwatch_db;

-- Connect to the database
-- \c stopwatch_db

-- Create stopwatch records table
CREATE TABLE IF NOT EXISTS stopwatch_records (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(100) NOT NULL,
    client_id VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    client_timestamp TIMESTAMP NOT NULL,
    additional_data TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_stopwatch_records_event_id ON stopwatch_records(event_id);
CREATE INDEX IF NOT EXISTS idx_stopwatch_records_client_id ON stopwatch_records(client_id);
CREATE INDEX IF NOT EXISTS idx_stopwatch_records_timestamp ON stopwatch_records(client_timestamp);
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_stopwatch_record
ON stopwatch_records (event_id, client_id, type, client_timestamp);

-- Create API keys table for authentication (optional)
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    api_key VARCHAR(64) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    last_used_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS event_runners (
    id SERIAL PRIMARY KEY,
    event_id VARCHAR(100) NOT NULL,
    runner_id VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    start_time TIMESTAMP NULL,
    finish_time TIMESTAMP NULL,
    additional_data TEXT
);
CREATE INDEX IF NOT EXISTS idx_event_runners_event_id ON event_runners(event_id);