<?php
require_once '../config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stopwatch Records Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding-top: 20px; }
        .record-card { margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Stopwatch Records Dashboard</h1>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Event Filter
                    </div>
                    <div class="card-body">
                        <form id="filterForm">
                            <div class="mb-3">
                                <label for="eventId" class="form-label">Event ID</label>
                                <input type="text" class="form-control" id="eventId" placeholder="Enter event ID">
                            </div>
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <button type="button" class="btn btn-secondary" id="clearFilter">Clear</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        Server Time
                    </div>
                    <div class="card-body">
                        <h5 id="serverTime">Loading...</h5>
                        <p>Timezone: UTC</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="records-container">
            <h2>Records <small class="text-muted" id="recordCount"></small></h2>
            <div id="records" class="row"></div>
        </div>
    </div>

    <script>
        // Update server time
        function updateServerTime() {
            fetch('../api/time')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('serverTime').textContent = new Date(data.time).toLocaleString();
                });
        }
        
        // Load records
        function loadRecords(eventId = null) {
            let url = '../api/stopwatch/records';
            if (eventId) {
                url = `../api/stopwatch/events/${eventId}/records`;
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    const recordsContainer = document.getElementById('records');
                    recordsContainer.innerHTML = '';
                    
                    const records = data.records || [];
                    document.getElementById('recordCount').textContent = `(${records.length})`;
                    
                    records.forEach(record => {
                        const clientTime = new Date(parseInt(record.client_timestamp)).toLocaleString();
                        const cardColor = getCardColorByType(record.type);
                        
                        const recordCard = document.createElement('div');
                        recordCard.className = 'col-md-4';
                        recordCard.innerHTML = `
                            <div class="card record-card ${cardColor}">
                                <div class="card-header">
                                    ${record.type.toUpperCase()} - ${record.event_id}
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">Client: ${record.client_id}</h5>
                                    <p class="card-text">Time: ${clientTime}</p>
                                    <p class="card-text">Additional Data: ${record.additional_data || 'None'}</p>
                                </div>
                            </div>
                        `;
                        
                        recordsContainer.appendChild(recordCard);
                    });
                })
                .catch(error => {
                    console.error('Error loading records:', error);
                });
        }
        
        function getCardColorByType(type) {
            switch (type.toLowerCase()) {
                case 'start':
                    return 'bg-success text-white';
                case 'lap':
                    return 'bg-info text-white';
                case 'stop':
                    return 'bg-danger text-white';
                default:
                    return '';
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateServerTime();
            setInterval(updateServerTime, 1000);
            
            loadRecords();
            
            // Handle filter form
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const eventId = document.getElementById('eventId').value.trim();
                if (eventId) {
                    loadRecords(eventId);
                }
            });
            
            // Handle clear filter
            document.getElementById('clearFilter').addEventListener('click', function() {
                document.getElementById('eventId').value = '';
                loadRecords();
            });
        });
    </script>
</body>
</html>