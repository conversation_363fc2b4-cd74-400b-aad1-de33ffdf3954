<?php
require_once 'config.php';

// Set headers for CORS if enabled
if (ALLOW_CORS) {
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization");
}

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Set content type to JSON
header("Content-Type: application/json");

// Parse the URL path
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path_parts = explode('/', trim($path, '/'));


// Remove base path if needed (adjust as necessary)
if (count($path_parts) > 0 && $path_parts[0] === 'accurace') {
    array_shift($path_parts);
}

if (count($path_parts) > 0 && $path_parts[0] === 'webservice') {
    array_shift($path_parts);
}

if (count($path_parts) > 0 && $path_parts[0] === 'api') {
    array_shift($path_parts);
}

// Route the request
if (empty($path_parts)) {
    // Base API endpoint
    echo json_encode([
        'status' => 'success',
        'message' => 'Stopwatch API is running',
        'version' => '1.0.0'
    ]);
    exit;
}

// Handle different endpoints
switch ($path_parts[0]) {
    case 'time':
        require_once 'time_controller.php';
        handleTimeRequest();
        break;
        if (
            isset($path_parts[1]) &&
            isset($path_parts[2]) &&
            $path_parts[2] === 'runners'
        ) {
            require_once 'records_controller.php';
            handleEventRunnersRequest($path_parts[1]);
        } elseif (
            isset($path_parts[1]) &&
            isset($path_parts[2]) &&
            $path_parts[2] === 'records'
        ) {
            require_once 'records_controller.php';
            handleEventRecordsRequest($path_parts[1]);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Not found']);
        }
        break;
    case 'stopwatch':
        if (isset($path_parts[1])) {
            switch ($path_parts[1]) {
                case 'records':
                    require_once 'records_controller.php';
                    handleRecordsRequest();
                    break;

                case 'events':
                    if (isset($path_parts[2]) && isset($path_parts[3]) && $path_parts[3] === 'records') {
                        require_once 'records_controller.php';
                        handleEventRecordsRequest($path_parts[2]);
                    } else {
                        http_response_code(404);
                        echo json_encode(['error' => 'Not found']);
                    }
                    break;

                default:
                    http_response_code(404);
                    echo json_encode(['error' => 'Not found']);
                    break;
            }
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Not found']);
        }
        break;

    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found']);
        break;
}
