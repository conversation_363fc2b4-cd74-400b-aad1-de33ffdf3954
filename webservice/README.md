# Stopwatch API Server

This is the server-side component for the Stopwatch application, providing PostgreSQL database integration.

## Setup Instructions

### Prerequisites
- PHP 7.4 or higher
- PostgreSQL 12 or higher
- Apache or Nginx web server
- PDO PHP extension with PostgreSQL support

### Database Setup
1. Create a PostgreSQL database:
   ```sql
   CREATE DATABASE stopwatch_db;
   ```

2. Create a database user:
   ```sql
   CREATE USER postgres_user WITH PASSWORD 'your_secure_password';
   GRANT ALL PRIVILEGES ON DATABASE stopwatch_db TO postgres_user;
   ```

3. Import the schema:
   ```bash
   psql -U postgres_user -d stopwatch_db -f schema.sql
   ```

### Server Configuration
1. Update `config.php` with your database credentials
2. Ensure the web server has write permissions to the log directory
3. Configure your web server to use the provided `.htaccess` file

### Apache Configuration
Make sure mod_rewrite is enabled:
```bash
sudo a2enmod rewrite
sudo service apache2 restart
```

### Testing the API
You can test the API using curl:

```bash
# Test time endpoint
curl -X GET https://your-postgresql-api.com/api/time

# Test records endpoint
curl -X GET https://your-postgresql-api.com/api/stopwatch/records

# Test event records endpoint
curl -X GET https://your-postgresql-api.com/api/stopwatch/events/race1/records
```

## API Endpoints

### Time Synchronization
- `GET /api/time` - Get current server time

### Stopwatch Records
- `POST /api/stopwatch/records` - Sync records from client
- `GET /api/stopwatch/records` - Get all records
- `GET /api/stopwatch/events/{eventId}/records` - Get records for specific event

## Security Considerations
- Use HTTPS in production
- Consider implementing rate limiting
- Update the API key in config.php
- Consider implementing JWT authentication for more secure access